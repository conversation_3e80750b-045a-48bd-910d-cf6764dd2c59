# Security Headers for <PERSON><PERSON> <PERSON><PERSON> Neurosurgery Website
# These headers enhance security and performance

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=()

  # Enhanced Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co https://www.googletagmanager.com https://www.google-analytics.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https: blob:; connect-src 'self' https://www.google-analytics.com https://www.googletagmanager.com; frame-src 'self' https://www.google.com https://www.youtube.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;

  # HSTS (HTTP Strict Transport Security)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

  # Additional Security Headers
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin

  # Performance Headers
  Cache-Control: public, max-age=31536000, immutable

# Specific rules for different file types

# HTML files
/*.html
  Cache-Control: public, max-age=3600, must-revalidate

# CSS files
/*.css
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css; charset=utf-8

# JavaScript files
/*.js
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript; charset=utf-8

# Image files
/*.jpg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/jpeg

/*.jpeg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/jpeg

/*.png
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/png

/*.webp
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/webp

/*.svg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/svg+xml

# Font files
/*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff

/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2

/*.ttf
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/ttf

# Manifest and service worker
/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/manifest+json

/sw.js
  Cache-Control: public, max-age=0, must-revalidate
  Content-Type: application/javascript

# API routes (if any)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Sitemap and robots
/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml

/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain
