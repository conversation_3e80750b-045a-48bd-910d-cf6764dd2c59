# miNEURO Website

This is the official repository for the miNEURO website, a neurosurgery and spine surgery practice website built with modern web technologies.

## Project Info

**URL**: https://lovable.dev/projects/96f629c9-6031-4f68-8bd0-680a3c64b6e3

## Documentation

Comprehensive documentation is available in the `docs/` directory:

- [Getting Started](./docs/getting-started.md) - Setup and development workflow
- [Project Structure](./docs/project-structure.md) - Overview of the codebase organization
- [Accessibility Guidelines](./docs/accessibility.md) - Ensuring the website is accessible to all users
- [Internationalization Guide](./docs/internationalization.md) - Working with multiple languages
- [Deployment Guide](./docs/deployment.md) - How to deploy the website
- [Contributing Guidelines](./docs/contributing.md) - How to contribute to the project

### Component Documentation

- [Components Overview](./docs/components/overview.md) - Introduction to the component system
- [Layout Component](./docs/components/layout.md) - Main layout wrapper
- [SafeImage Component](./docs/components/safeimage.md) - Enhanced image component

## Technologies

This project is built with:

- [Vite](https://vitejs.dev/) - Build tool and development server
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- [React](https://reactjs.org/) - UI library
- [React Router](https://reactrouter.com/) - Client-side routing
- [shadcn/ui](https://ui.shadcn.com/) - Component library built on Radix UI
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [React Query](https://tanstack.com/query/latest) - Data fetching and state management

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v16 or later)
- [npm](https://www.npmjs.com/) (v7 or later)

### Installation

```sh
# Step 1: Clone the repository
git clone https://github.com/r70pro/neuro.git

# Step 2: Navigate to the project directory
cd neuro

# Step 3: Install dependencies
npm install

# Step 4: Start the development server
npm run dev
```

The website should now be running at [http://localhost:5173](http://localhost:5173).

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check for code issues

## Editing Options

### Use Lovable

Visit the [Lovable Project](https://lovable.dev/projects/96f629c9-6031-4f68-8bd0-680a3c64b6e3) and start prompting.
Changes made via Lovable will be committed automatically to this repo.

### Use your preferred IDE

Clone this repo and push changes. Pushed changes will also be reflected in Lovable.

### Edit a file directly in GitHub

- Navigate to the desired file(s)
- Click the "Edit" button (pencil icon) at the top right of the file view
- Make your changes and commit the changes

### Use GitHub Codespaces

- Navigate to the main page of your repository
- Click on the "Code" button (green button) near the top right
- Select the "Codespaces" tab
- Click on "New codespace" to launch a new Codespace environment
- Edit files directly within the Codespace and commit and push your changes

## Deployment

### Quick Deployment

Open [Lovable](https://lovable.dev/projects/96f629c9-6031-4f68-8bd0-680a3c64b6e3) and click on Share -> Publish.

### Custom Domain

For custom domain setup, we recommend using Netlify. See the [Deployment Guide](./docs/deployment.md) and [Custom domains](https://docs.lovable.dev/tips-tricks/custom-domain/) for more details.

## Contributing

Please read our [Contributing Guidelines](./docs/contributing.md) before submitting pull requests or opening issues.

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
