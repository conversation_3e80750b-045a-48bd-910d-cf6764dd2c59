
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 210 40% 10%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 10%;

    --primary: 210 100% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 210 40% 10%;

    --muted: 210 40% 96%;
    --muted-foreground: 210 30% 40%;

    --accent: 210 40% 96%;
    --accent-foreground: 210 40% 10%;

    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 210 100% 40%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 210 30% 12%;
    --foreground: 210 20% 98%;

    --card: 210 30% 15%;
    --card-foreground: 210 20% 98%;

    --popover: 210 30% 15%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 30% 20%;
    --secondary-foreground: 210 20% 98%;

    --muted: 210 30% 20%;
    --muted-foreground: 210 20% 60%;

    --accent: 210 30% 20%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 210 20% 98%;

    --border: 210 30% 25%;
    --input: 210 30% 25%;
    --ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
}

@layer components {
  .container {
    @apply px-4 md:px-6 lg:px-8 mx-auto;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground rounded-full px-6 py-2.5 font-medium transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 active:scale-[0.98];
  }

  .section {
    @apply py-12 md:py-16 lg:py-24;
  }

  .glass-card {
    @apply bg-white/70 dark:bg-black/30 backdrop-blur-lg border border-white/20 dark:border-white/10 rounded-xl shadow-lg;
  }

  /* Mobile-optimized components */
  .mobile-container {
    @apply px-mobile-md sm:px-mobile-lg md:px-mobile-xl lg:px-mobile-2xl mx-auto;
  }

  .mobile-section {
    @apply py-mobile-xl sm:py-mobile-2xl md:py-12 lg:py-16;
  }

  .mobile-card {
    @apply bg-card rounded-lg shadow-sm border border-border/50 p-mobile-lg transition-all duration-200;
  }

  .mobile-button {
    @apply min-h-[44px] min-w-[44px] touch-manipulation select-none transition-all duration-200 active:scale-95;
  }

  .mobile-input {
    @apply min-h-[44px] text-base touch-manipulation;
  }

  .mobile-text {
    @apply text-mobile-base leading-relaxed;
  }

  .mobile-heading {
    @apply text-mobile-2xl font-bold leading-tight;
  }

  .mobile-subheading {
    @apply text-mobile-lg font-semibold leading-snug;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-feedback {
    @apply transition-transform duration-100 active:scale-95 touch-manipulation;
  }

  .swipe-container {
    @apply overflow-x-auto scrollbar-hide touch-pan-x;
  }

  .wave-animation {
    animation: wave 12s linear infinite;
    animation-delay: -2s;
    transform-origin: centre bottom;
  }

  .page-transition-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 400ms, transform 400ms;
  }

  .page-transition-exit {
    opacity: 1;
  }

  .page-transition-exit-active {
    opacity: 0;
    transition: opacity 300ms;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .mobile-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Hide scrollbars on mobile */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-specific styles */
  @media (hover: none) and (pointer: coarse) {
    .hover-only {
      display: none;
    }

    .touch-only {
      display: block;
    }

    /* Larger touch targets on touch devices */
    button, a, input, select, textarea {
      min-height: 44px;
    }

    /* Remove hover effects on touch devices */
    .hover\:scale-105:hover {
      transform: none;
    }

    .hover\:shadow-lg:hover {
      box-shadow: none;
    }
  }

  @media (hover: hover) and (pointer: fine) {
    .touch-only {
      display: none;
    }

    .hover-only {
      display: block;
    }
  }

  /* Mobile typography improvements */
  @media (max-width: 640px) {
    h1 {
      font-size: 2rem;
      line-height: 2.25rem;
    }

    h2 {
      font-size: 1.75rem;
      line-height: 2rem;
    }

    h3 {
      font-size: 1.5rem;
      line-height: 1.75rem;
    }

    h4 {
      font-size: 1.25rem;
      line-height: 1.5rem;
    }

    p {
      font-size: 0.95rem;
      line-height: 1.5rem;
    }
  }
}
