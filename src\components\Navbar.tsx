import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageSelector from "@/components/LanguageSelector";
import SafeImage from "@/components/SafeImage";
import SkipLink from "@/components/SkipLink";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/contexts/LanguageContext";
import { useDeviceDetection, useSwipeGesture } from "@/hooks/use-mobile";

export default function Navbar() {
  const { t } = useLanguage();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const deviceInfo = useDeviceDetection();

  // Enhanced mobile menu close function
  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setActiveSubmenu(null);
    // Remove scroll lock from body
    document.body.classList.remove('mobile-no-scroll');
  };

  // Enhanced mobile menu open function
  const openMobileMenu = () => {
    setMobileMenuOpen(true);
    // Prevent body scroll when mobile menu is open
    document.body.classList.add('mobile-no-scroll');
  };

  // Swipe gesture for closing mobile menu
  const swipeHandlers = useSwipeGesture(
    () => {
      // Swipe left to close menu
      if (mobileMenuOpen) {
        closeMobileMenu();
      }
    },
    undefined, // No right swipe action
    undefined, // No up swipe action
    undefined, // No down swipe action
    100 // Threshold for swipe detection
  );

  const navLinks = [
    { name: t.nav.home, path: "/" },
    {
      name: t.nav.expertise,
      path: "/expertise",
      submenu: [
        { name: t.nav.expertiseSubmenu.cervicalDisc, path: "/expertise/cervical-disc-replacement" },
        { name: t.nav.expertiseSubmenu.lumbarDisc, path: "/expertise/lumbar-disc-replacement" },
        { name: t.nav.expertiseSubmenu.imageGuided, path: "/expertise/image-guided-surgery" },
        { name: t.nav.expertiseSubmenu.roboticSpine, path: "/expertise/robotic-spine-surgery" },
      ]
    },
    { name: t.nav.appointments, path: "/appointments" },
    {
      name: t.patientResources.title,
      path: "/patient-resources",
      submenu: [
        { name: t.patientResources.submenu.comprehensiveSolution, path: "/patient-resources/individual-spine-health-programme" },
        { name: t.patientResources.submenu.conditionInfo, path: "/patient-resources/condition-information" },
        { name: t.patientResources.submenu.exerciseLibrary, path: "/patient-resources/exercise-library" },
        { name: t.nav.faq, path: "/faq" },
      ]
    },
    {
      name: t.nav.gpResources,
      path: "/gp-resources",
      submenu: [
        { name: t.nav.gpResourcesSubmenu.referralProtocols, path: "/gp-resources/referral-protocols" },
        { name: t.nav.gpResourcesSubmenu.diagnostics, path: "/gp-resources/diagnostics" },
        { name: t.nav.gpResourcesSubmenu.careCoordination, path: "/gp-resources/care-coordination" },
        { name: t.nav.gpResourcesSubmenu.emergencies, path: "/gp-resources/emergencies" },
      ]
    },
    { name: t.nav.medicolegal, path: "/medicolegal" },
    {
      name: t.nav.locations,
      path: "/locations",
      submenu: [
        { name: t.nav.locationsSubmenu.mornington, path: "/locations/mornington" },
        { name: t.nav.locationsSubmenu.frankston, path: "/locations/frankston" },
        { name: t.nav.locationsSubmenu.langwarrin || "Langwarrin", path: "/locations/langwarrin" },
        { name: t.nav.locationsSubmenu.mooneePonds, path: "/locations/moonee-ponds" },
        { name: t.nav.locationsSubmenu.sunbury, path: "/locations/sunbury" },
        { name: t.nav.locationsSubmenu.werribee, path: "/locations/werribee" },
        { name: t.nav.locationsSubmenu.bundoora, path: "/locations/bundoora" },
        { name: t.nav.locationsSubmenu.dandenong, path: "/locations/dandenong" },
        { name: t.nav.locationsSubmenu.heidelberg, path: "/locations/heidelberg" },
        { name: t.nav.locationsSubmenu.wantirna, path: "/locations/wantirna" },
        { name: t.nav.locationsSubmenu.surreyHills, path: "/locations/surrey-hills" },
      ]
    },
    { name: t.nav.contact, path: "/contact" }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };
    window.addEventListener("scroll", handleScroll);

    // Close submenu when clicking outside
    const handleClickOutside = (e: MouseEvent) => {
      if (activeSubmenu && !(e.target as Element).closest('.nav-item-with-submenu')) {
        setActiveSubmenu(null);
      }
    };
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [scrolled, activeSubmenu]);

  // Function to toggle submenu
  const toggleSubmenu = (name: string, e: React.MouseEvent) => {
    // Check if the click was on the dropdown arrow or near the right edge
    const target = e.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    const isClickNearRightEdge = (e.clientX > rect.right - 30);

    if (isClickNearRightEdge) {
      // If clicking near the right edge (dropdown arrow), toggle the submenu
      e.preventDefault();
      setActiveSubmenu(activeSubmenu === name ? null : name);
    }
    // If clicking elsewhere on the link, the default navigation will occur
  };

  // Function to handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, name: string, hasSubmenu: boolean) => {
    if (hasSubmenu && e.key === 'ArrowDown') {
      // Arrow down always opens the submenu
      e.preventDefault();
      setActiveSubmenu(name);
    } else if (hasSubmenu && (e.key === 'Enter' || e.key === ' ')) {
      // Enter or Space navigates to the page if submenu is open, otherwise opens submenu
      if (activeSubmenu === name) {
        // Submenu is open, let the navigation happen
        return;
      } else {
        // Submenu is closed, open it
        e.preventDefault();
        setActiveSubmenu(name);
      }
    }
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm">
        <SkipLink />
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Language Selector */}
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center">
              <SafeImage
                src="/logo/logo.png"
                alt="miNEURO Logo"
                className="h-8 w-auto"
                fallbackSrc="/logo/logo-placeholder.png"
              />
            </Link>
            <LanguageSelector />
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map(link => (
              <div key={link.name} className="relative">
                {link.submenu ? (
                  <>
                    <button
                      className="flex items-center space-x-1 text-gray-700 dark:text-gray-200 hover:text-primary font-medium transition-colors duration-200"
                      onClick={(e) => toggleSubmenu(link.name, e)}
                      onKeyDown={(e) => handleKeyDown(e, link.name, true)}
                      aria-expanded={activeSubmenu === link.name}
                      aria-haspopup="true"
                    >
                      <span>{link.name}</span>
                      <span className="text-xs">▾</span>
                    </button>

                    {/* Dropdown Menu */}
                    {activeSubmenu === link.name && (
                      <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">
                        {link.submenu.map((subItem) => (
                          <Link
                            key={subItem.name}
                            to={subItem.path}
                            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary transition-colors"
                            onClick={() => setActiveSubmenu(null)}
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </>
                ) : (
                  <Link
                    to={link.path}
                    className="text-gray-700 dark:text-gray-200 hover:text-primary font-medium transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* Right Side - Theme Toggle and Book Now */}
          <div className="hidden lg:flex items-center space-x-4">
            <ThemeToggle />
            <Button asChild className="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-full font-medium">
              <Link to="/appointments">{t.nav.bookNow}</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex items-center space-x-2">
            <ThemeToggle />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => mobileMenuOpen ? closeMobileMenu() : openMobileMenu()}
              className="rounded-full"
              aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={mobileMenuOpen}
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
        </nav>
      </header>

      {/* Mobile Menu */}
      <div
        className={cn("fixed inset-0 z-40 bg-background/80 mobile-backdrop md:hidden transition-opacity duration-300",
        mobileMenuOpen ? "opacity-100" : "opacity-0 pointer-events-none")}
        role="dialog"
        aria-modal="true"
        aria-label="Mobile navigation menu"
        id="mobile-menu"
        onClick={(e) => {
          // Close menu when clicking on backdrop
          if (e.target === e.currentTarget) {
            closeMobileMenu();
          }
        }}
      >
        <div
          className={cn("fixed inset-y-0 right-0 w-4/5 max-w-sm bg-card shadow-xl mobile-safe-area transition-transform duration-300 ease-in-out",
          mobileMenuOpen ? "translate-x-0" : "translate-x-full")}
          {...swipeHandlers}
        >
          <div className="flex flex-col h-full justify-between p-mobile-lg">
            <div>
              <div className="flex justify-between items-center mb-mobile-xl">
                <div className="flex items-center gap-mobile-md">
                  <Link to="/" onClick={closeMobileMenu} className="flex items-center touch-feedback">
                    <SafeImage
                      src="/logo/logo.png"
                      alt="miNEURO Logo"
                      className="h-10 w-auto"
                      fallbackSrc="/logo/logo-placeholder.png"
                    />
                  </Link>
                  <LanguageSelector />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={closeMobileMenu}
                  className="mobile-button rounded-full touch-feedback"
                  aria-label="Close menu"
                >
                  <X className="h-6 w-6" aria-hidden="true" />
                </Button>
              </div>
              <nav aria-label="Mobile navigation">
                <ul className="space-y-mobile-lg" role="menu">
                  {navLinks.map(link => (
                    <li key={link.name} role="none">
                      <Link
                        to={link.path}
                        className="mobile-text font-medium transition-colors hover:text-primary block mobile-button touch-feedback py-mobile-md"
                        onClick={(e) => {
                          // Check if the click was on the dropdown arrow
                          const target = e.currentTarget as HTMLElement;
                          const rect = target.getBoundingClientRect();
                          const isClickNearRightEdge = (e.clientX > rect.right - 44); // Larger touch target

                          if (link.submenu && isClickNearRightEdge) {
                            // If clicking on the dropdown arrow, toggle submenu
                            e.preventDefault();
                            setActiveSubmenu(activeSubmenu === link.name ? null : link.name);
                          } else {
                            // Otherwise navigate to the page
                            closeMobileMenu();
                          }
                        }}
                        aria-expanded={link.submenu ? activeSubmenu === link.name : undefined}
                        aria-haspopup={link.submenu ? 'true' : undefined}
                        role="menuitem"
                      >
                        {link.name}
                        {link.submenu && (
                          <span className="ml-1 inline-block float-right text-xl leading-none touch-target">
                            {activeSubmenu === link.name ? '▴' : '▾'}
                          </span>
                        )}
                      </Link>

                      {/* Mobile submenu */}
                      {link.submenu && (
                        <div
                          className={cn(
                            "pl-mobile-lg mt-mobile-sm space-y-mobile-sm transition-all duration-200 overflow-hidden",
                            activeSubmenu === link.name
                              ? 'max-h-96 opacity-100'
                              : 'max-h-0 opacity-0'
                          )}
                          role="menu"
                          aria-label={`${link.name} submenu`}
                        >
                          {link.submenu.map((subItem) => (
                            <Link
                              key={subItem.name}
                              to={subItem.path}
                              className="block mobile-text text-muted-foreground hover:text-primary mobile-button touch-feedback py-mobile-sm"
                              onClick={closeMobileMenu}
                              role="menuitem"
                            >
                              {subItem.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            <Button asChild className="w-full btn-primary mobile-button touch-feedback mt-mobile-xl">
              <Link to="/appointments" onClick={closeMobileMenu}>
                {t.nav.bookNow}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
